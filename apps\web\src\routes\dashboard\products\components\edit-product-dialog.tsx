import { useForm } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { trpcClient } from "@/utils/trpc";

// Validation schema
const updateProductSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug is required"),
});

type UpdateProductFormData = z.infer<typeof updateProductSchema>;

interface EditProductDialogProps {
  product: {
    id: string;
    name: string;
    description?: string | null;
    slug: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

export function EditProductDialog({
  product,
  open,
  onOpenChange,
  onSuccess,
}: EditProductDialogProps) {
  const updateProductMutation = useMutation({
    mutationFn: async (data: UpdateProductFormData & { id: string }) => {
      return trpcClient.product.update.mutate({
        id: data.id,
        name: data.name,
        description: data.description,
        slug: data.slug,
      });
    },
    onSuccess: () => {
      toast.success("Product updated successfully!");
      onOpenChange(false);
      form.reset();

      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update product");
    },
  });

  const form = useForm({
    defaultValues: {
      name: product.name,
      description: product.description || "",
      slug: product.slug,
    } as UpdateProductFormData,
    onSubmit: async ({ value }) => {
      updateProductMutation.mutate({
        id: product.id,
        ...value,
      });
    },
    validators: {
      onSubmit: updateProductSchema,
    },
  });

  // Auto-generate slug when name changes
  const handleNameChange = (name: string) => {
    form.setFieldValue("name", name);
    if (name && name !== product.name) {
      const generatedSlug = generateSlug(name);
      form.setFieldValue("slug", generatedSlug);
    }
  };

  // Reset form when dialog opens/closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Product</DialogTitle>
          <DialogDescription>
            Update the product information. Changes will be saved immediately.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-4"
        >
          <div>
            <form.Field name="name">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Product Name</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., Canva Pro"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => handleNameChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="slug">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>URL Slug</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., canva-pro"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be used in the URL: /dashboard/products/
                    {field.state.value || "your-slug"}
                  </p>
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="description">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Description (Optional)</Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    placeholder="Describe your product..."
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    rows={3}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <form.Subscribe>
              {(state) => (
                <Button
                  type="submit"
                  disabled={
                    !state.canSubmit ||
                    state.isSubmitting ||
                    updateProductMutation.isPending
                  }
                >
                  {state.isSubmitting || updateProductMutation.isPending
                    ? "Updating..."
                    : "Update Product"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
